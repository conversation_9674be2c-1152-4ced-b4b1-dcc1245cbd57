const position = { x: 0, y: 0 };

export let initialNodes = [
  // 供应端
  {
    id: "supply",
    position,
    data: {
      label: "供应端",
    },
    class: "node_0",
  },
  {
    id: "led_materials",
    position,
    data: {
      label: "LED封装材料",
    },
    class: "node_1",
  },
  {
    id: "substrate",
    position,
    data: {
      label: "衬底基板",
    },
    class: "node_1",
  },
  {
    id: "led_chips",
    position,
    data: {
      label: "LED芯片",
    },
    class: "node_1",
  },
  {
    id: "led_driver",
    position,
    data: {
      label: "引线框架",
    },
    class: "node_1",
  },
  {
    id: "packaging",
    position,
    data: {
      label: "封装胶",
    },
    class: "node_1",
  },
  {
    id: "low_temp_materials",
    position,
    data: {
      label: "低温填充料",
    },
    class: "node_1",
  },
  {
    id: "led_package",
    position,
    data: {
      label: "LED封装",
    },
    class: "node_1",
  },
  {
    id: "sapphire_materials",
    position,
    data: {
      label: "蓝宝石材料",
    },
    class: "node_1",
  },
  {
    id: "sapphire_components",
    position,
    data: {
      label: "蓝宝石元件",
    },
    class: "node_1",
  },
  {
    id: "electronic_components",
    position,
    data: {
      label: "电子元器件",
    },
    class: "node_1",
  },
  {
    id: "electronic_thermal",
    position,
    data: {
      label: "电子散热器",
    },
    class: "node_1",
  },
  {
    id: "led_optical_materials",
    position,
    data: {
      label: "LED背光源及材料",
    },
    class: "node_1",
  },
  {
    id: "led_backlight",
    position,
    data: {
      label: "LED背光源",
    },
    class: "node_1",
  },
  {
    id: "ccfl_backlight",
    position,
    data: {
      label: "CCFL背光源",
    },
    class: "node_1",
  },
  {
    id: "mobile_backlight",
    position,
    data: {
      label: "手机背光源",
    },
    class: "node_1",
  },
  {
    id: "display_backlight",
    position,
    data: {
      label: "显示器背光源",
    },
    class: "node_1",
  },
  {
    id: "display_panel_equipment",
    position,
    data: {
      label: "显示面板设备",
    },
    class: "node_1",
  },
  {
    id: "single_board",
    position,
    data: {
      label: "单板玻璃",
    },
    class: "node_1",
  },
  {
    id: "cover_board",
    position,
    data: {
      label: "盖板玻璃",
    },
    class: "node_1",
  },
  {
    id: "current_board",
    position,
    data: {
      label: "导电玻璃",
    },
    class: "node_1",
  },
  {
    id: "protection_board",
    position,
    data: {
      label: "保护玻璃",
    },
    class: "node_1",
  },
  {
    id: "led_drive_power",
    position,
    data: {
      label: "LED驱动电源",
    },
    class: "node_1",
  },
  {
    id: "led_control_system",
    position,
    data: {
      label: "LED控制系统",
    },
    class: "node_1",
  },
  {
    id: "led_bracket",
    position,
    data: {
      label: "LED支架",
    },
    class: "node_1",
  },
  {
    id: "led_extension",
    position,
    data: {
      label: "LED延片",
    },
    class: "node_1",
  },
  {
    id: "external_extension",
    position,
    data: {
      label: "外延延片",
    },
    class: "node_1",
  },
  {
    id: "storage_extension",
    position,
    data: {
      label: "储存延片",
    },
    class: "node_1",
  },
  // 生产端
  {
    id: "production",
    position,
    data: {
      label: "生产端",
    },
    class: "node_0",
  },
  {
    id: "led_display",
    position,
    data: {
      label: "LED显示屏",
    },
    class: "node_1",
  },
  {
    id: "monochrome_led",
    position,
    data: {
      label: "单色LED显示屏",
    },
    class: "node_1",
  },
  {
    id: "led_text",
    position,
    data: {
      label: "LED图文屏",
    },
    class: "node_1",
  },
  {
    id: "led_video",
    position,
    data: {
      label: "LED视频屏",
    },
    class: "node_1",
  },
  {
    id: "led_indoor",
    position,
    data: {
      label: "LED室内屏",
    },
    class: "node_1",
  },
  {
    id: "led_lighting_tools",
    position,
    data: {
      label: "LED照明灯具",
    },
    class: "node_1",
  },
  {
    id: "led_landscape_light",
    position,
    data: {
      label: "LED景观灯",
    },
    class: "node_1",
  },
  {
    id: "led_bulb",
    position,
    data: {
      label: "LED球泡灯",
    },
    class: "node_1",
  },
  {
    id: "led_strip_light",
    position,
    data: {
      label: "LED灯条",
    },
    class: "node_1",
  },
  {
    id: "led_high_power",
    position,
    data: {
      label: "LED大功率灯",
    },
    class: "node_1",
  },
  {
    id: "automotive_led",
    position,
    data: {
      label: "汽车灯具配件",
    },
    class: "node_1",
  },
  {
    id: "headlight",
    position,
    data: {
      label: "前照灯",
    },
    class: "node_1",
  },
  {
    id: "work_light",
    position,
    data: {
      label: "车工作灯",
    },
    class: "node_1",
  },
  {
    id: "brake_light",
    position,
    data: {
      label: "车尾灯",
    },
    class: "node_1",
  },
  {
    id: "indicator_light",
    position,
    data: {
      label: "示廓灯",
    },
    class: "node_1",
  },
  // 应用端
  {
    id: "application",
    position,
    data: {
      label: "应用端",
    },
    class: "node_0",
  },
  {
    id: "display_application_system",
    position,
    data: {
      label: "显示屏应用系统",
    },
    class: "node_1",
  },
  {
    id: "aviation_display_system",
    position,
    data: {
      label: "航空显示系统",
    },
    class: "node_1",
  },
  {
    id: "display_usage_system",
    position,
    data: {
      label: "显示屏用接系统",
    },
    class: "node_1",
  },
  {
    id: "industrial_display_equipment",
    position,
    data: {
      label: "工控显示设备",
    },
    class: "node_1",
  },
  {
    id: "optical_standard_display_system",
    position,
    data: {
      label: "光电标准显示系统",
    },
    class: "node_1",
  },
  {
    id: "lighting_engineering",
    position,
    data: {
      label: "照明工程",
    },
    class: "node_1",
  },
  {
    id: "urban_lighting_engineering",
    position,
    data: {
      label: "城市照明工程",
    },
    class: "node_1",
  },
  {
    id: "stage_lighting_engineering",
    position,
    data: {
      label: "舞台灯光工程",
    },
    class: "node_1",
  },
  {
    id: "stage_lighting",
    position,
    data: {
      label: "舞台灯具",
    },
    class: "node_1",
  },
  {
    id: "video_display_service",
    position,
    data: {
      label: "影视显示服务",
    },
    class: "node_1",
  },
  {
    id: "storage_equipment_service",
    position,
    data: {
      label: "仓储设备及服务",
    },
    class: "node_1",
  },
  {
    id: "special_vehicles",
    position,
    data: {
      label: "专用车辆",
    },
    class: "node_1",
  },
  {
    id: "travel_vehicle",
    position,
    data: {
      label: "旅行车",
    },
    class: "node_1",
  },
  {
    id: "suv_vehicle",
    position,
    data: {
      label: "越野车用车",
    },
    class: "node_1",
  },
  {
    id: "super_suv_vehicle",
    position,
    data: {
      label: "超级越野车",
    },
    class: "node_1",
  },
  {
    id: "special_purpose_vehicle",
    position,
    data: {
      label: "专用商用车",
    },
    class: "node_1",
  },
];

export const initialEdges = [
  // 供应端到LED封装的连接
  { id: "e-supply-led_package", source: "supply", target: "led_package" },

  // 供应端内部材料连接
  { id: "e-supply-led_materials", source: "supply", target: "led_materials" },
  { id: "e-supply-substrate", source: "supply", target: "substrate" },
  { id: "e-supply-led_chips", source: "supply", target: "led_chips" },
  { id: "e-supply-led_driver", source: "supply", target: "led_driver" },
  { id: "e-supply-packaging", source: "supply", target: "packaging" },
  {
    id: "e-supply-low_temp_materials",
    source: "supply",
    target: "low_temp_materials",
  },

  {
    id: "e-supply-sapphire_materials",
    source: "supply",
    target: "sapphire_materials",
  },
  {
    id: "e-supply-electronic_components",
    source: "supply",
    target: "electronic_components",
  },
  {
    id: "e-supply-led_optical_materials",
    source: "supply",
    target: "led_optical_materials",
  },
  {
    id: "e-supply-display_panel_equipment",
    source: "supply",
    target: "display_panel_equipment",
  },
  {
    id: "e-supply-led_drive_power",
    source: "supply",
    target: "led_drive_power",
  },
  { id: "e-supply-led_extension", source: "supply", target: "led_extension" },

  // LED封装到生产端的连接
  {
    id: "e-led_package-production",
    source: "led_package",
    target: "production",
  },

  // 生产端内部连接
  {
    id: "e-production-led_display",
    source: "production",
    target: "led_display",
  },
  {
    id: "e-production-led_lighting_tools",
    source: "production",
    target: "led_lighting_tools",
  },
  {
    id: "e-production-automotive_led",
    source: "production",
    target: "automotive_led",
  },

  // 生产端到应用端的连接
  {
    id: "e-production-application",
    source: "production",
    target: "application",
  },

  // 应用端内部连接
  {
    id: "e-application-display_application_system",
    source: "application",
    target: "display_application_system",
  },
  {
    id: "e-application-lighting_engineering",
    source: "application",
    target: "lighting_engineering",
  },
  {
    id: "e-application-stage_lighting_engineering",
    source: "application",
    target: "stage_lighting_engineering",
  },
  {
    id: "e-application-special_vehicles",
    source: "application",
    target: "special_vehicles",
  },

  // 一些子类别连接
  {
    id: "e-led_display-monochrome_led",
    source: "led_display",
    target: "monochrome_led",
  },
  { id: "e-led_display-led_text", source: "led_display", target: "led_text" },
  { id: "e-led_display-led_video", source: "led_display", target: "led_video" },
  {
    id: "e-led_display-led_indoor",
    source: "led_display",
    target: "led_indoor",
  },

  {
    id: "e-led_lighting_tools-led_landscape_light",
    source: "led_lighting_tools",
    target: "led_landscape_light",
  },
  {
    id: "e-led_lighting_tools-led_bulb",
    source: "led_lighting_tools",
    target: "led_bulb",
  },
  {
    id: "e-led_lighting_tools-led_strip_light",
    source: "led_lighting_tools",
    target: "led_strip_light",
  },
  {
    id: "e-led_lighting_tools-led_high_power",
    source: "led_lighting_tools",
    target: "led_high_power",
  },

  {
    id: "e-automotive_led-headlight",
    source: "automotive_led",
    target: "headlight",
  },
  {
    id: "e-automotive_led-work_light",
    source: "automotive_led",
    target: "work_light",
  },
  {
    id: "e-automotive_led-brake_light",
    source: "automotive_led",
    target: "brake_light",
  },
  {
    id: "e-automotive_led-indicator_light",
    source: "automotive_led",
    target: "indicator_light",
  },
];
